#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版数据处理工具函数
基于原始utilts.py，修正location和year列的处理问题
保持全局插值策略
"""

import pandas as pd
import numpy as np
from sklearn.impute import KNNImputer
from sklearn.preprocessing import MinMaxScaler

def filter_variables(df, missing_threshold=0.5, variance_threshold=0.01, 
                    exclude_columns=['location', 'year']):
    """
    修正版变量筛选函数，排除指定的标识列
    
    :param df: 输入的 DataFrame
    :param missing_threshold: 缺失值比例阈值，默认 0.5（50%）
    :param variance_threshold: 方差阈值，默认 0.01
    :param exclude_columns: 需要排除筛选的列名列表，默认['location', 'year']
    :return: 处理后的 DataFrame
    """
    print(f"开始变量筛选，排除标识列: {exclude_columns}")
    
    # 确定实际存在的排除列
    existing_exclude = [col for col in exclude_columns if col in df.columns]
    print(f"实际存在的排除列: {existing_exclude}")
    
    # 1. 剔除缺失值比例大于 missing_threshold 的变量（排除标识列）
    # 只对非排除列计算缺失值比例
    columns_to_check_missing = [col for col in df.columns if col not in existing_exclude]
    missing_ratio = df[columns_to_check_missing].isnull().mean()
    columns_to_drop_missing = missing_ratio[missing_ratio > missing_threshold].index
    
    print(f"剔除缺失值比例大于 {missing_threshold} 的变量数量: {len(columns_to_drop_missing)}")
    if len(columns_to_drop_missing) > 0:
        print(f"剔除的变量: {list(columns_to_drop_missing)}")
    
    # 2. 剔除方差小于 variance_threshold 的变量（排除标识列）
    # 只对非排除列中的数值列计算方差
    numeric_columns_to_check = df[columns_to_check_missing].select_dtypes(include=['number']).columns
    variances = df[numeric_columns_to_check].var()
    columns_to_drop_variance = variances[variances < variance_threshold].index
    
    print(f"剔除方差小于 {variance_threshold} 的变量数量: {len(columns_to_drop_variance)}")
    if len(columns_to_drop_variance) > 0:
        print(f"剔除的变量: {list(columns_to_drop_variance)}")
    
    # 3. 执行删除操作
    all_columns_to_drop = list(columns_to_drop_missing) + list(columns_to_drop_variance)
    df_filtered = df.drop(columns=all_columns_to_drop)
    
    print(f"筛选结果:")
    print(f"  - 原始列数: {len(df.columns)}")
    print(f"  - 删除列数: {len(all_columns_to_drop)}")
    print(f"  - 保留列数: {len(df_filtered.columns)}")
    print(f"  - 其中标识列: {len(existing_exclude)}")
    print(f"  - 其中协变量列: {len(df_filtered.columns) - len(existing_exclude)}")
    
    return df_filtered


def min_max_knn_impute(df, n_neighbors=5, weights='uniform', 
                      exclude_columns=['location', 'year']):
    """
    修正版KNN插值函数，排除指定的标识列，采用全局插值策略
    
    :param df: 输入的 DataFrame
    :param n_neighbors: KNN 的邻居数量，默认 5
    :param weights: 权重计算方式，默认 'uniform'（等权重），可选 'distance'（按距离加权）
    :param exclude_columns: 不参与标准化和插值的列名列表，默认['location', 'year']
    :return: 填充后的 DataFrame
    """
    print(f"开始KNN插值，排除标识列: {exclude_columns}")
    print("采用全局插值策略（不分组）")
    
    # 确定实际存在的排除列
    existing_exclude = [col for col in exclude_columns if col in df.columns]
    print(f"实际存在的排除列: {existing_exclude}")
    
    # 1. 选择需要处理的数值列（排除标识列）
    # 先获取所有数值列，然后排除标识列
    all_numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
    numeric_columns = [col for col in all_numeric_columns if col not in existing_exclude]
    
    print(f"总数值列数: {len(all_numeric_columns)}")
    print(f"排除标识列后的数值列数: {len(numeric_columns)}")
    
    # 检查缺失值情况
    missing_before = df[numeric_columns].isnull().sum().sum()
    print(f"插值前缺失值总数: {missing_before}")
    
    if missing_before == 0:
        print("无缺失值，跳过插值处理")
        return df
    
    # 2. 提取需要处理的数值数据
    df_numeric = df[numeric_columns]
    
    # 3. Min-Max 标准化
    print("执行Min-Max标准化...")
    scaler = MinMaxScaler()
    df_scaled = pd.DataFrame(
        scaler.fit_transform(df_numeric), 
        columns=numeric_columns,
        index=df_numeric.index
    )
    
    # 4. KNN 插值（全局策略）
    print(f"执行KNN插值（邻居数: {n_neighbors}，权重: {weights}）...")
    imputer = KNNImputer(n_neighbors=n_neighbors, weights=weights)
    df_imputed_scaled = pd.DataFrame(
        imputer.fit_transform(df_scaled), 
        columns=numeric_columns,
        index=df_scaled.index
    )
    
    # 5. 反标准化，将数据还原到原始范围
    print("执行反标准化...")
    df_imputed = pd.DataFrame(
        scaler.inverse_transform(df_imputed_scaled), 
        columns=numeric_columns,
        index=df_imputed_scaled.index
    )
    
    # 6. 将插值后的数值列合并回原始 DataFrame
    df_filled = df.copy()
    df_filled[numeric_columns] = df_imputed
    
    # 验证插值结果
    missing_after = df_filled[numeric_columns].isnull().sum().sum()
    print(f"插值后缺失值总数: {missing_after}")
    print(f"成功填充缺失值: {missing_before - missing_after}")
    
    # 验证标识列是否保持不变
    for col in existing_exclude:
        if col in df.columns:
            original_values = df[col].fillna('NaN_PLACEHOLDER')
            filled_values = df_filled[col].fillna('NaN_PLACEHOLDER')
            if original_values.equals(filled_values):
                print(f"✓ {col} 列保持不变")
            else:
                print(f"⚠ 警告: {col} 列发生了变化")
    
    return df_filled


def check_data_info(df, title="数据信息"):
    """
    检查和显示数据基本信息的辅助函数
    """
    print(f"\n{title}:")
    print(f"  - 数据形状: {df.shape}")
    
    # 检查location和year列
    if 'location' in df.columns:
        unique_locations = df['location'].nunique()
        print(f"  - location列: {unique_locations} 个不同值")
    
    if 'year' in df.columns:
        if df['year'].dtype in ['int64', 'float64']:
            year_range = f"{df['year'].min():.0f}-{df['year'].max():.0f}"
            print(f"  - year列: 范围 {year_range}")
        else:
            print(f"  - year列: 数据类型 {df['year'].dtype}")
    
    # 检查缺失值
    total_missing = df.isnull().sum().sum()
    missing_ratio = total_missing / (df.shape[0] * df.shape[1]) * 100
    print(f"  - 缺失值: {total_missing} ({missing_ratio:.2f}%)")
    
    # 检查数据类型
    numeric_cols = len(df.select_dtypes(include=['number']).columns)
    object_cols = len(df.select_dtypes(include=['object']).columns)
    print(f"  - 数值列: {numeric_cols}, 字符列: {object_cols}")


# 为了保持与原始utilts.py的兼容性，保留原始函数名的导入
# 但实际调用修正版函数
def filter_variables_original_compatible(df, missing_threshold=0.5, variance_threshold=0.01):
    """
    与原始filter_variables函数兼容的包装函数
    自动排除location和year列
    """
    return filter_variables(df, missing_threshold, variance_threshold, ['location', 'year'])


def min_max_knn_impute_original_compatible(df, n_neighbors=5, weights='uniform'):
    """
    与原始min_max_knn_impute函数兼容的包装函数
    自动排除location和year列
    """
    return min_max_knn_impute(df, n_neighbors, weights, ['location', 'year'])
