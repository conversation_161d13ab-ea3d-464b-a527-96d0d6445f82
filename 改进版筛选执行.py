#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版变量筛选执行脚本
正确处理location和year列
"""

import pandas as pd
from utilts_improved import filter_variables_improved, min_max_knn_impute_improved, check_data_structure

def main():
    print("=" * 60)
    print("改进版协变量筛选处理")
    print("=" * 60)
    
    # 1. 读取原始数据
    print("\n步骤1: 读取原始数据...")
    try:
        df = pd.read_csv("194个国家1960-2023存在缺失值的751个协变量.csv")
        print(f"✓ 成功读取数据，形状: {df.shape}")
    except Exception as e:
        print(f"✗ 读取失败: {e}")
        return False
    
    # 2. 检查数据结构
    print("\n步骤2: 检查数据结构...")
    structure_info = check_data_structure(df)
    
    # 3. 确定排除列
    exclude_columns = ['location', 'year']
    
    # 检查这些列是否存在
    existing_exclude = [col for col in exclude_columns if col in df.columns]
    missing_exclude = [col for col in exclude_columns if col not in df.columns]
    
    print(f"\n步骤3: 确定排除列...")
    print(f"指定排除列: {exclude_columns}")
    print(f"实际存在的排除列: {existing_exclude}")
    if missing_exclude:
        print(f"⚠ 警告: 以下排除列不存在: {missing_exclude}")
    
    # 4. 执行变量筛选
    print(f"\n步骤4: 执行变量筛选...")
    try:
        filtered_df = filter_variables_improved(
            df=df,
            missing_threshold=0.5,
            variance_threshold=0.01,
            exclude_columns=existing_exclude
        )
        print(f"✓ 变量筛选完成")
    except Exception as e:
        print(f"✗ 变量筛选失败: {e}")
        return False
    
    # 5. 执行KNN插值
    print(f"\n步骤5: 执行改进版KNN插值...")
    try:
        filled_df = min_max_knn_impute_improved(
            df=filtered_df,
            n_neighbors=5,
            weights='uniform',
            exclude_columns=existing_exclude,
            use_location_grouping=True  # 按location分组插值
        )
        print(f"✓ KNN插值完成")
    except Exception as e:
        print(f"✗ KNN插值失败: {e}")
        return False
    
    # 6. 验证结果
    print(f"\n步骤6: 验证处理结果...")
    
    # 检查标识列是否保留
    for col in existing_exclude:
        if col in filled_df.columns:
            print(f"✓ {col} 列已保留")
            if col == 'location':
                unique_locations = filled_df[col].nunique()
                print(f"  - 包含 {unique_locations} 个不同的location")
            elif col == 'year':
                year_range = f"{filled_df[col].min():.0f}-{filled_df[col].max():.0f}"
                print(f"  - 年份范围: {year_range}")
        else:
            print(f"✗ 警告: {col} 列丢失")
    
    # 检查数据完整性
    final_missing = filled_df.isnull().sum().sum()
    print(f"最终缺失值数量: {final_missing}")
    
    # 7. 保存结果
    print(f"\n步骤7: 保存结果...")
    try:
        output_file = "筛选后协变量特征值列表.csv"
        filled_df.to_csv(output_file, index=False)
        print(f"✓ 结果已保存: {output_file}")
        
        # 显示最终统计
        print(f"\n最终处理统计:")
        print(f"  - 原始数据: {df.shape}")
        print(f"  - 最终数据: {filled_df.shape}")
        print(f"  - 变量保留率: {filled_df.shape[1] / df.shape[1] * 100:.1f}%")
        print(f"  - 标识列数: {len(existing_exclude)}")
        print(f"  - 协变量列数: {filled_df.shape[1] - len(existing_exclude)}")
        
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("处理完成！")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✓ 所有步骤成功完成")
        print("现在可以运行主notebook进行后续分析")
    else:
        print("\n✗ 处理过程中出现错误")
