
#
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.stats import rankdata
import numpy as np

# RandomWiring类用于初始化神经网络的连接
class RandomWiring:
    def __init__(self, input_dim, output_dim, neuron_count):
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.neuron_count = neuron_count
        self.adjacency_matrix = torch.rand(neuron_count, neuron_count)  # Neuron-to-neuron connections
        self.sensory_adjacency_matrix = torch.rand(input_dim, neuron_count)  # Input-to-neuron connections

    def erev_initializer(self):
        return torch.rand(self.neuron_count, self.neuron_count) * 0.4 - 0.2

    def sensory_erev_initializer(self):
        return torch.rand(self.input_dim, self.neuron_count) * 0.4 - 0.2


class LIFNeuronLayer(nn.Module):
    def __init__(self, wiring, ode_unfolds=12, epsilon=1e-8, device='cuda:0'):
        super(LIFNeuronLayer, self).__init__()
        self.wiring = wiring
        self.ode_unfolds = ode_unfolds
        self.epsilon = epsilon
        self.device = device
        self.softplus = nn.Softplus()

        # Initialize parameters
        self.gleak = nn.Parameter(torch.rand(wiring.neuron_count) * 0.999 + 0.001)
        self.vleak = nn.Parameter(torch.rand(wiring.neuron_count) * 0.4 - 0.2)
        self.cm = nn.Parameter(torch.rand(wiring.neuron_count) * 0.2 + 0.4)
        self.w = nn.Parameter(torch.rand(wiring.neuron_count, wiring.neuron_count) * 0.999 + 0.001)
        self.sigma = nn.Parameter(torch.rand(wiring.neuron_count, wiring.neuron_count) * 5 + 3)
        self.mu = nn.Parameter(torch.rand(wiring.neuron_count, wiring.neuron_count) * 0.5 + 0.3)
        self.erev = nn.Parameter(wiring.erev_initializer())

        # Sensory parameters
        self.sensory_w = nn.Parameter(torch.rand(wiring.input_dim, wiring.neuron_count) * 0.999 + 0.001)
        self.sensory_sigma = nn.Parameter(torch.rand(wiring.input_dim, wiring.neuron_count) * 5 + 3)
        self.sensory_mu = nn.Parameter(torch.rand(wiring.input_dim, wiring.neuron_count) * 0.5 + 0.3)
        self.sensory_erev = nn.Parameter(wiring.sensory_erev_initializer())

        self.sparsity_mask = torch.Tensor(torch.abs(wiring.adjacency_matrix)).to(self.device)
        self.sensory_sparsity_mask = torch.Tensor(torch.abs(wiring.sensory_adjacency_matrix)).to(self.device)

    def forward(self, inputs, state, elapsed_time=1.0):
        return self.ode_solver(inputs, state, elapsed_time)

    def ode_solver(self, inputs, state, elapsed_time):
        v_pre = state
        sensory_activation = self.softplus(self.sensory_w) * self.sigmoid(inputs, self.sensory_mu, self.sensory_sigma)
        sensory_activation = sensory_activation * self.sensory_sparsity_mask
        sensory_reversal_activation = sensory_activation * self.sensory_erev

        w_numerator_sensory = torch.sum(sensory_reversal_activation, dim=1)
        w_denominator_sensory = torch.sum(sensory_activation, dim=1)

        cm_t = self.softplus(self.cm) / (elapsed_time / self.ode_unfolds)
        w_param = self.softplus(self.w)

        for _ in range(self.ode_unfolds):
            w_activation = w_param * self.sigmoid(v_pre, self.mu, self.sigma)
            w_activation = w_activation * self.sparsity_mask
            reversal_activation = w_activation * self.erev

            w_numerator = torch.sum(reversal_activation, dim=1) + w_numerator_sensory
            w_denominator = torch.sum(w_activation, dim=1) + w_denominator_sensory

            gleak = self.softplus(self.gleak)
            numerator = cm_t * v_pre + gleak * self.vleak + w_numerator
            denominator = cm_t + gleak + w_denominator

            v_pre = numerator / (denominator + self.epsilon)

        return v_pre

    def sigmoid(self, v_pre, mu, sigma):
        #print(f"v_pre shape: {v_pre.shape}")  # 打印 v_pre 的形状
        #print(f"mu shape: {mu.shape}")        # 打印 mu 的形状
        #print(f"sigma shape: {sigma.shape}")  # 打印 sigma 的形状

        # 调整维度使得它们能够广播匹配
        v_pre = torch.unsqueeze(v_pre, -1)
        #print(f"v_pre after unsqueeze: {v_pre.shape}")  # 打印调整后 v_pre 的形状

        # 执行sigmoid计算
        activation = sigma * (v_pre - mu)
        return torch.sigmoid(activation)

#org
class LTCCell(nn.Module):
    def __init__(self, wiring, in_features=None, ode_unfolds=6, epsilon=1e-8, device='cuda:0'):
        super(LTCCell, self).__init__()
        self.neuron = LIFNeuronLayer(wiring, ode_unfolds, epsilon, device)

    def forward(self, inputs, states, elapsed_time=1.0):
        next_state = self.neuron(inputs, states, elapsed_time)
        #print(f"LTCCell output shape: {next_state.shape}")  # 打印 LTCCell 的输出形状
        outputs = next_state[:, :self.neuron.wiring.output_dim]
        return outputs, next_state


#org
# class LTCRNN(nn.Module):
#     def __init__(self, input_dim, hidden_dim, output_dim=1, device='cuda:0'):
#         super(LTCRNN, self).__init__()
#         wiring = RandomWiring(input_dim=input_dim, neuron_count=hidden_dim, output_dim=output_dim)
#         self.cell = LTCCell(wiring, in_features=input_dim, device=device)
#         self.device = device
#
#     def forward(self, inputs):
#         batch_size, seq_len, _ = inputs.size()
#         states = torch.zeros(batch_size, self.cell.neuron.wiring.neuron_count).to(self.device)
#         outputs = []
#
#         for t in range(seq_len):
#             output, states = self.cell(inputs[:, t, :], states)
#             #print(f"LTCRNN output at timestep {t} shape: {output.shape}")  # 打印每个时间步的输出形状
#             outputs.append(output)
#
#         result = torch.stack(outputs, dim=1)
#         #print(f"Final LTCRNN output shape: {result.shape}")  # 打印最终输出的形状
#         return result[:, -1, :].squeeze()

class LTCRNN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim=1, device='cuda:0'):
        super(LTCRNN, self).__init__()
        wiring = RandomWiring(input_dim=input_dim, neuron_count=hidden_dim, output_dim=output_dim)
        self.cell = LTCCell(wiring, in_features=input_dim, device=device)
        self.device = device

    def forward(self, inputs):
        batch_size, seq_len, _ = inputs.size()
        states = torch.zeros(batch_size, self.cell.neuron.wiring.neuron_count).to(self.device)
        outputs = []

        for t in range(seq_len):
            output, states = self.cell(inputs[:, t, :], states)
            outputs.append(output)

        result = torch.stack(outputs, dim=1)
        # 对输出应用 sigmoid 激活函数
        return torch.sigmoid(result[:, -1, :].squeeze())

# class LNN(nn.Module):
#     def __init__(self, input_dim, hidden_size, output_dim=1, device='cuda:0', lr=0.001, optimizer='adam', loss='mse'):
#         super(LNN, self).__init__()
#
#         self.lnn_model = LTCRNN(input_dim=input_dim, hidden_dim=hidden_size, output_dim=output_dim, device=device)
#         self.device = device
#         self.optimizer = optimizer.lower()
#         self.loss = loss
#         self.lr = lr
#
#         if optimizer == 'adam':
#             self.train_optimizer = optim.Adam(self.lnn_model.parameters(), lr=self.lr)
#         elif optimizer == 'gd':
#             self.train_optimizer = optim.SGD(self.lnn_model.parameters(), lr=self.lr)
#         else:
#             raise NotImplementedError(f"Optimizer {optimizer} is not supported")
#
#         self.lnn_model.to(self.device)
#         # 初始化 SoftRankLoss
#         if self.loss == 'soft_rank':
#             self.rank_loss_fn = SoftRankLoss(tau=0.5)  # 使用 soft rank loss
#
#     def forward(self, x):
#         return self.lnn_model(x)
#
#     def loss_fn(self, pred, label):
#         if self.loss == 'mse':
#             return torch.mean((pred - label) ** 2)
#
#         elif self.loss == 'ic':
#             # 计算 -IC
#             return -self.compute_pearson_ic(pred, label)  # 负号表示最大化 IC
#
#         elif self.loss == 'soft_rank':
#             return self.rank_loss_fn(pred, label)  # 使用 Soft Rank 损失
#
#         else:
#             raise ValueError(f"Unknown loss type {self.loss}")

class LNN(nn.Module):
    def __init__(self, input_dim, hidden_size, output_dim=1, device='cuda:0', lr=0.001, optimizer='adam',
                 loss='bce'):
        super(LNN, self).__init__()

        self.lnn_model = LTCRNN(input_dim=input_dim, hidden_dim=hidden_size, output_dim=output_dim, device=device)
        self.device = device
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.lr = lr

        if optimizer == 'adam':
            self.train_optimizer = optim.Adam(self.lnn_model.parameters(), lr=self.lr)
        elif optimizer == 'gd':
            self.train_optimizer = optim.SGD(self.lnn_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError(f"Optimizer {optimizer} is not supported")

        self.lnn_model.to(self.device)

        # 初始化损失函数
        if self.loss == 'bce':
            self.loss_fn = nn.BCELoss()  # 二分类交叉熵损失
        elif self.loss == 'bce_with_logits':
            self.loss_fn = nn.BCEWithLogitsLoss()  # 带 logits 的二分类交叉熵损失
        else:
            raise ValueError(f"Unknown loss type {self.loss}")

    def forward(self, x):
        return self.lnn_model(x)

    def loss_fn(self, pred, label):
        return self.loss_fn(pred, label)  # 直接使用定义好的损失函数
    def compute_pearson_ic(self, pred, labels):
        """
        计算预测值和标签的皮尔逊相关系数（Pearson IC）在每一行上的平均值。

        参数:
        pred (torch.Tensor): 预测值张量，形状为 [batch_size, output_dim]
        labels (torch.Tensor): 真实值张量，形状为 [batch_size, output_dim]

        返回:
        float: 所有行的皮尔逊相关系数（Pearson IC）的平均值
        """
        # 直接在 PyTorch 上计算皮尔逊相关系数，不脱离计算图
        #print("pred shape in compute IC",pred.shape)
        #print("labels shape in compute IC",labels.shape)
        if labels.dim() == 3:  # 如果 labels 有一个额外的维度，去掉它
            labels = labels.squeeze(1)  # 去掉维度 1，即变成 [batch_size, output_dim]

        # 确保 pred 和 labels 形状一致
        assert pred.shape == labels.shape, f"Shape mismatch: pred shape {pred.shape}, labels shape {labels.shape}"

        pred_mean = torch.mean(pred, dim=1, keepdim=True)
        labels_mean = torch.mean(labels, dim=1, keepdim=True)

        pred_centered = pred - pred_mean  # 减去均值，中心化
        labels_centered = labels - labels_mean

        # 计算皮尔逊相关系数
        numerator = torch.sum(pred_centered * labels_centered, dim=1)
        denominator = torch.sqrt(torch.sum(pred_centered ** 2, dim=1)) * torch.sqrt(
            torch.sum(labels_centered ** 2, dim=1))

        # 皮尔逊相关系数
        pearson_ic = numerator / denominator

        # 返回所有行的平均皮尔逊相关系数
        return torch.mean(pearson_ic)  # 返回 PyTorch 张量，支持反向传播


class SoftRankLoss(nn.Module):
    def __init__(self, tau=1.0):
        super(SoftRankLoss, self).__init__()
        self.tau = tau  # 温度参数，用于平滑排名

    def forward(self, pred, labels):
        # 使用 softmax 来计算概率分布
        pred = torch.softmax(pred / self.tau, dim=-1)
        labels = torch.softmax(labels / self.tau, dim=-1)

        # 计算预测值和标签的 "soft" 排名相关性（Cosine 相似度也可以考虑）
        loss = torch.mean((pred - labels) ** 2)  # 这里可以根据实际需要选择其他损失函数

        return loss

