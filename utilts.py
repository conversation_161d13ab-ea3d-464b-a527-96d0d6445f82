import pandas
import numpy
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import torch
import numpy as np
from scipy.stats import pearsonr
from pytorch_lnn_ts import *
from pytorch_lnn_ts import LNN
import torch
import torch.nn as nn
import torch.optim as optim
from scipy.stats import rankdata
import numpy as np
import torch
import torch.nn as nn
from sklearn.metrics import accuracy_score, roc_auc_score, f1_score, confusion_matrix, roc_curve
from sklearn.model_selection import train_test_split
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

#填充缺失值
import pandas as pd
from sklearn.impute import KNNImputer
from sklearn.preprocessing import MinMaxScaler


import pandas as pd
def filter_variables(df, missing_threshold=0.5, variance_threshold=0.01):
    """
    剔除缺失值比例大于 missing_threshold 的变量，以及方差小于 variance_threshold 的变量。
    :param df: 输入的 DataFrame
    :param missing_threshold: 缺失值比例阈值，默认 0.5（50%）
    :param variance_threshold: 方差阈值，默认 0.01
    :return: 处理后的 DataFrame
    """
    # 1. 剔除缺失值比例大于 missing_threshold 的变量
    missing_ratio = df.isnull().mean()  # 计算每列的缺失值比例
    columns_to_drop_missing = missing_ratio[missing_ratio > missing_threshold].index
    df = df.drop(columns=columns_to_drop_missing)

    print(f"剔除缺失值比例大于 {missing_threshold} 的变量: {list(columns_to_drop_missing)}")

    # 2. 剔除方差小于 variance_threshold 的变量
    numeric_columns = df.select_dtypes(include=['number']).columns  # 只对数值列计算方差
    variances = df[numeric_columns].var()  # 计算每列的方差
    columns_to_drop_variance = variances[variances < variance_threshold].index
    df = df.drop(columns=columns_to_drop_variance)

    print(f"剔除方差小于 {variance_threshold} 的变量: {list(columns_to_drop_variance)}")

    return df


def min_max_knn_impute(df, n_neighbors=5, weights='uniform'):
    """
    先对数据进行 Min-Max 标准化，再进行 KNN 插值。
    :param df: 输入的 DataFrame
    :param n_neighbors: KNN 的邻居数量，默认 5
    :param weights: 权重计算方式，默认 'uniform'（等权重），可选 'distance'（按距离加权）
    :return: 填充后的 DataFrame
    """
    # 1. 选择数值列
    numeric_columns = df.select_dtypes(include=['number']).columns
    df_numeric = df[numeric_columns]

    # 2. Min-Max 标准化
    scaler = MinMaxScaler()
    df_scaled = pd.DataFrame(scaler.fit_transform(df_numeric), columns=numeric_columns)

    # 3. KNN 插值
    imputer = KNNImputer(n_neighbors=n_neighbors, weights=weights)
    df_imputed_scaled = pd.DataFrame(imputer.fit_transform(df_scaled), columns=numeric_columns)

    # 4. 反标准化，将数据还原到原始范围
    df_imputed = pd.DataFrame(scaler.inverse_transform(df_imputed_scaled), columns=numeric_columns)

    # 5. 将插值后的数值列合并回原始 DataFrame
    df_filled = df.copy()
    df_filled[numeric_columns] = df_imputed

    return df_filled

def get_next_year(df, year_col_name):
    """
    该函数用于获取表格中最后一行year列的下一年时间。
    若year列的数据为datetime格式，会将其转换为整数年份进行计算。
    :param df: 输入的DataFrame表格
    :param year_col_name: year列的列名
    :return: 最后一行year列的下一年时间
    """
    # 获取最后一行year列的值
    last_year = df[year_col_name].iloc[-1]
    # 检查数据类型
    if pd.api.types.is_datetime64_any_dtype(df[year_col_name]):
        # 如果是datetime类型，提取年份并转换为整数
        last_year = int(last_year.year)
    else:
        # 确保是整数类型
        last_year = int(last_year)

    # 返回下一年的时间
    return last_year + 1


def standardize_features(features):
    """
    对输入的特征数据进行 0 - 1 标准化
    :param features: 输入的特征数据 DataFrame
    :return: 标准化后的特征数据 DataFrame 和标准化器对象
    """
    scaler = MinMaxScaler()
    features_scaled = pd.DataFrame(scaler.fit_transform(features), columns=features.columns, index=features.index)
    return features_scaled, scaler

def inverse_standardize_prediction(prediction_df, scaler):
    """
    对预测结果进行逆标准化
    :param prediction_df: 预测结果的 DataFrame
    :param scaler: 标准化器对象
    :return: 逆标准化后的预测结果 DataFrame
    """
    inverse_prediction = scaler.inverse_transform(prediction_df)
    return pd.DataFrame(inverse_prediction, columns=prediction_df.columns)

# 检查数据是否有空值
def check_nan_inf(data, name):
    if isinstance(data, np.ndarray):
        if np.isnan(data).any() or np.isinf(data).any():
            print(f"{name} contains NaN or Inf values.")
            data = np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)  # 将 NaN 和 Inf 替换为 0
            print(f"Replaced NaN/Inf in {name} with 0.")
        return data
    elif isinstance(data, torch.Tensor):
        if torch.isnan(data).any() or torch.isinf(data).any():
            print(f"{name} contains NaN or Inf values.")
            data = torch.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)  # 将 NaN 和 Inf 替换为 0
            print(f"Replaced NaN/Inf in {name} with 0.")
        return data
    else:
        raise ValueError(f"Unsupported data type: {type(data)}")


import threading
import time


# 定义一个超时异常类
class TimeoutError(Exception):
    pass


# 定义一个超时处理函数
def run_with_timeout(func, timeout, *args, **kwargs):
    # 定义一个包装函数
    def wrapper():
        try:
            wrapper.result = func(*args, **kwargs)
        except Exception as e:
            wrapper.exception = e

    # 创建线程并运行
    wrapper.result = None
    wrapper.exception = None
    thread = threading.Thread(target=wrapper)
    thread.start()

    # 等待线程完成或超时
    thread.join(timeout)

    # 检查线程是否完成
    if thread.is_alive():
        # 如果线程仍在运行，说明超时
        raise TimeoutError("Code execution timed out")
    elif wrapper.exception is not None:
        # 如果线程抛出异常，重新抛出
        raise wrapper.exception
    else:
        # 返回结果
        return wrapper.result

def split_data_0step_test(data, timestep, feature_size, sequence_length, ratio_train=0.6, ratio_val=0.2):
    dataX = []  # 保存X
    dataY = []  # 保存Y

    # 将整个窗口的数据保存到X中，将未来一天保存到Y中
    for index in range(len(data) - timestep - sequence_length + 1):
        dataX.append(data[index: index + timestep])
        dataY.append(list(data[index + timestep: index + timestep + sequence_length].values))

    dataX = np.array(dataX)
    dataY = np.array(dataY)
    print("dataY:", dataY.shape)
    # 获取数据集大小
    data_size = dataX.shape[0]

    # 划分训练集、验证集、测试集
    test_size = 0
    val_size = int(np.round(ratio_val * (data_size - test_size)))
    train_size = data_size - val_size - test_size

    x_train = dataX[:train_size]
    y_train = dataY[:train_size]
    print(x_train.shape)
    x_val = dataX[train_size:train_size + val_size]
    y_val = dataY[train_size:train_size + val_size]

    x_test = dataX[train_size + val_size:]
    y_test = dataY[train_size + val_size:]

    # 重塑训练集、验证集、测试集的形状
    x_train = x_train.reshape(-1, timestep, feature_size)
    # y_train = y_train.reshape(-1, sequence_length,)

    x_val = x_val.reshape(-1, timestep, feature_size)
    # y_val = y_val.reshape(-1, sequence_length)

    x_test = x_test.reshape(-1, timestep, feature_size)
    # y_test = y_test.reshape(-1, sequence_length)

    return [x_train, y_train, x_val, y_val, x_test, y_test, dataY]