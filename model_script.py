import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Data<PERSON>oa<PERSON>, TensorDataset
from sklearn.model_selection import train_test_split
import numpy as np

class RandomWiring:
    def __init__(self, input_dim, output_dim, neuron_count):
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.neuron_count = neuron_count
        self.adjacency_matrix = torch.rand(neuron_count, neuron_count)  # Neuron-to-neuron connections
        self.sensory_adjacency_matrix = torch.rand(input_dim, neuron_count)  # Input-to-neuron connections

    def erev_initializer(self):
        return torch.rand(self.neuron_count, self.neuron_count) * 0.4 - 0.2

    def sensory_erev_initializer(self):
        return torch.rand(self.input_dim, self.neuron_count) * 0.4 - 0.2


class LIFNeuronLayer(nn.Module):
    def __init__(self, wiring, ode_unfolds=12, epsilon=1e-8, device='cuda:0'):
        super(LIFNeuronLayer, self).__init__()
        self.wiring = wiring
        self.ode_unfolds = ode_unfolds
        self.epsilon = epsilon
        self.device = device
        self.softplus = nn.Softplus()

        # Initialize parameters
        self.gleak = nn.Parameter(torch.rand(wiring.neuron_count) * 0.999 + 0.001)
        self.vleak = nn.Parameter(torch.rand(wiring.neuron_count) * 0.4 - 0.2)
        self.cm = nn.Parameter(torch.rand(wiring.neuron_count) * 0.2 + 0.4)
        self.w = nn.Parameter(torch.rand(wiring.neuron_count, wiring.neuron_count) * 0.999 + 0.001)
        self.sigma = nn.Parameter(torch.rand(wiring.neuron_count, wiring.neuron_count) * 5 + 3)
        self.mu = nn.Parameter(torch.rand(wiring.neuron_count, wiring.neuron_count) * 0.5 + 0.3)
        self.erev = nn.Parameter(wiring.erev_initializer())

        # Sensory parameters
        self.sensory_w = nn.Parameter(torch.rand(wiring.input_dim, wiring.neuron_count) * 0.999 + 0.001)
        self.sensory_sigma = nn.Parameter(torch.rand(wiring.input_dim, wiring.neuron_count) * 5 + 3)
        self.sensory_mu = nn.Parameter(torch.rand(wiring.input_dim, wiring.neuron_count) * 0.5 + 0.3)
        self.sensory_erev = nn.Parameter(wiring.sensory_erev_initializer())

        self.sparsity_mask = torch.Tensor(torch.abs(wiring.adjacency_matrix)).to(self.device)
        self.sensory_sparsity_mask = torch.Tensor(torch.abs(wiring.sensory_adjacency_matrix)).to(self.device)

    def forward(self, inputs, state, elapsed_time=1.0):
        return self.ode_solver(inputs, state, elapsed_time)

    def ode_solver(self, inputs, state, elapsed_time):
        v_pre = state
        sensory_activation = self.softplus(self.sensory_w) * self.sigmoid(inputs, self.sensory_mu, self.sensory_sigma)
        sensory_activation = sensory_activation * self.sensory_sparsity_mask
        sensory_reversal_activation = sensory_activation * self.sensory_erev

        w_numerator_sensory = torch.sum(sensory_reversal_activation, dim=1)
        w_denominator_sensory = torch.sum(sensory_activation, dim=1)

        cm_t = self.softplus(self.cm) / (elapsed_time / self.ode_unfolds)
        w_param = self.softplus(self.w)

        for _ in range(self.ode_unfolds):
            w_activation = w_param * self.sigmoid(v_pre, self.mu, self.sigma)
            w_activation = w_activation * self.sparsity_mask
            reversal_activation = w_activation * self.erev

            w_numerator = torch.sum(reversal_activation, dim=1) + w_numerator_sensory
            w_denominator = torch.sum(w_activation, dim=1) + w_denominator_sensory

            gleak = self.softplus(self.gleak)
            numerator = cm_t * v_pre + gleak * self.vleak + w_numerator
            denominator = cm_t + gleak + w_denominator

            v_pre = numerator / (denominator + self.epsilon)

        return v_pre

    def sigmoid(self, v_pre, mu, sigma):
        v_pre = torch.unsqueeze(v_pre, -1)
        activation = sigma * (v_pre - mu)
        return torch.sigmoid(activation)

class LTCCell(nn.Module):
    def __init__(self, wiring, in_features=None, ode_unfolds=6, epsilon=1e-8, device='cuda:0'):
        super(LTCCell, self).__init__()
        self.neuron = LIFNeuronLayer(wiring, ode_unfolds, epsilon, device)

    def forward(self, inputs, states, elapsed_time=1.0):
        next_state = self.neuron(inputs, states, elapsed_time)
        outputs = next_state[:, :self.neuron.wiring.output_dim]
        return outputs, next_state


class LTCRNN(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim=1, device='cuda:0'):
        super(LTCRNN, self).__init__()
        wiring = RandomWiring(input_dim=input_dim, neuron_count=hidden_dim, output_dim=output_dim)
        self.cell = LTCCell(wiring, in_features=input_dim, device=device)
        self.device = device

    def forward(self, inputs):
        batch_size, seq_len, _ = inputs.size()
        states = torch.zeros(batch_size, self.cell.neuron.wiring.neuron_count).to(self.device)
        outputs = []

        for t in range(seq_len):
            output, states = self.cell(inputs[:, t, :], states)
            outputs.append(output)

        result = torch.stack(outputs, dim=1)
        return result[:, -1, :].squeeze()


class LNN(nn.Module):
    def __init__(self, input_dim, hidden_size, output_dim=1, device='cuda:0', lr=0.001, optimizer='adam', loss='bce'):
        super(LNN, self).__init__()

        self.lnn_model = LTCRNN(input_dim=input_dim, hidden_dim=hidden_size, output_dim=output_dim, device=device)
        self.device = device
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.lr = lr

        if optimizer == 'adam':
            self.train_optimizer = optim.Adam(self.lnn_model.parameters(), lr=self.lr)
        elif optimizer == 'gd':
            self.train_optimizer = optim.SGD(self.lnn_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError(f"Optimizer {optimizer} is not supported")

        self.lnn_model.to(self.device)
        # 初始化 SoftRankLoss
        if self.loss == 'soft_rank':
            self.rank_loss_fn = SoftRankLoss(tau=0.5)  # 使用 soft rank loss

    def forward(self, x):
        output = self.lnn_model(x)
        return torch.sigmoid(output)  # 添加 sigmoid 激活函数，输出二分类概率

    def loss_fn(self, pred, label):
        if self.loss == 'bce':
            return nn.functional.binary_cross_entropy(pred, label)  # 使用二元交叉熵损失

        elif self.loss == 'soft_rank':
            return self.rank_loss_fn(pred, label)  # 使用 Soft Rank 损失

        else:
            raise ValueError(f"Unknown loss type {self.loss}")


class SoftRankLoss(nn.Module):
    def __init__(self, tau=1.0):
        super(SoftRankLoss, self).__init__()
        self.tau = tau  # 温度参数，用于平滑排名

    def forward(self, pred, labels):
        # 使用 softmax 来计算概率分布
        pred = torch.softmax(pred / self.tau, dim=-1)
        labels = torch.softmax(labels / self.tau, dim=-1)

        # 计算预测值和标签的 "soft" 排名相关性（Cosine 相似度也可以考虑）
        loss = torch.mean((pred - labels) ** 2)  # 这里可以根据实际需要选择其他损失函数

        return loss

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import torch
import gpytorch
#测试阶段
# 检查是否有可用的 GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")




def predict_next_row(features_scaled):
    """
    使用多输出高斯过程模型预测特征数据的下一行
    :param features_scaled: 标准化后的特征数据 DataFrame
    :return: 预测结果的 DataFrame 和评估指标字典
    """
    # 准备训练数据
    X_train = torch.tensor(np.arange(len(features_scaled)).reshape(-1, 1), dtype=torch.float32).to(device)
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 定义多输出高斯过程模型
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.RBFKernel(), num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    # 初始化 likelihood 和模型
    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)

    # 训练模型
    model.train()
    likelihood.train()

    # 使用 Adam 优化器
    optimizer = torch.optim.Adam([
        {'params': model.parameters()},  # Includes GaussianLikelihood parameters
    ], lr=0.1)

    # "Loss" for GPs - the marginal log likelihood
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    training_iter = 100
    for i in range(training_iter):
        # Zero gradients from previous iteration
        optimizer.zero_grad()
        # Output from model
        output = model(X_train)
        # Calc loss and backprop gradients
        loss = -mll(output, y_train)
        loss.backward()
        print('Iter %d/%d - Loss: %.3f' % (
            i + 1, training_iter, loss.item()
        ))
        optimizer.step()

    # 准备预测的输入
    next_time_step = torch.tensor([[len(features_scaled)]], dtype=torch.float32).to(device)

    # 进行预测
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        next_prediction = likelihood(model(next_time_step)).mean.cpu().numpy()

    del X_train, y_train, model, likelihood, optimizer, mll, output, loss
    torch.cuda.empty_cache()
    # 将预测结果转换为 DataFrame
    next_prediction_df = pd.DataFrame(next_prediction, columns=features_scaled.columns)

    return next_prediction_df