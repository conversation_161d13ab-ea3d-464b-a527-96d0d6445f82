#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版变量筛选执行脚本
基于原始utilts.py逻辑，修正location和year列处理问题
采用全局插值策略
"""

import pandas as pd
from utilts_fixed import filter_variables, min_max_knn_impute, check_data_info

def main():
    print("=" * 70)
    print("修正版协变量筛选处理（全局插值策略）")
    print("=" * 70)
    
    # 1. 读取原始数据
    print("\n步骤1: 读取原始数据")
    print("-" * 30)
    
    input_file = "194个国家1960-2023存在缺失值的751个协变量.csv"
    
    try:
        # 尝试不同编码读取文件
        for encoding in ['utf-8', 'gbk', 'latin-1']:
            try:
                df = pd.read_csv(input_file, encoding=encoding)
                print(f"✓ 使用 {encoding} 编码成功读取数据")
                break
            except UnicodeDecodeError:
                continue
        else:
            raise Exception("所有编码方式都失败")
            
    except FileNotFoundError:
        print(f"✗ 错误: 找不到文件 '{input_file}'")
        print("请确认文件名和路径是否正确")
        return False
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return False
    
    # 显示原始数据信息
    check_data_info(df, "原始数据信息")
    
    # 2. 检查关键列是否存在
    print("\n步骤2: 检查关键列")
    print("-" * 30)
    
    required_columns = ['location', 'year']
    existing_columns = []
    missing_columns = []
    
    for col in required_columns:
        if col in df.columns:
            existing_columns.append(col)
            print(f"✓ 找到 {col} 列")
        else:
            missing_columns.append(col)
            print(f"⚠ 未找到 {col} 列")
    
    if missing_columns:
        print(f"警告: 缺少关键列 {missing_columns}")
        print("将继续处理，但可能影响结果质量")
    
    # 3. 执行变量筛选
    print("\n步骤3: 执行变量筛选")
    print("-" * 30)
    print("筛选策略:")
    print("  - 排除 location 和 year 列的缺失值筛选")
    print("  - 排除 location 和 year 列的方差筛选")
    print("  - 缺失值阈值: 50%")
    print("  - 方差阈值: 0.01")
    
    try:
        filtered_df = filter_variables(
            df=df,
            missing_threshold=0.5,
            variance_threshold=0.01,
            exclude_columns=existing_columns
        )
        
        print(f"\n✓ 变量筛选完成")
        check_data_info(filtered_df, "筛选后数据信息")
        
    except Exception as e:
        print(f"✗ 变量筛选失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 执行KNN插值
    print("\n步骤4: 执行KNN插值")
    print("-" * 30)
    print("插值策略:")
    print("  - 排除 location 和 year 列的标准化处理")
    print("  - 排除 location 和 year 列作为KNN特征")
    print("  - 采用全局插值策略（不按location分组）")
    print("  - KNN邻居数: 5")
    print("  - 权重方式: uniform")
    
    try:
        filled_df = min_max_knn_impute(
            df=filtered_df,
            n_neighbors=5,
            weights='uniform',
            exclude_columns=existing_columns
        )
        
        print(f"\n✓ KNN插值完成")
        check_data_info(filled_df, "插值后数据信息")
        
    except Exception as e:
        print(f"✗ KNN插值失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 验证处理结果
    print("\n步骤5: 验证处理结果")
    print("-" * 30)
    
    # 验证标识列
    for col in existing_columns:
        if col in filled_df.columns:
            print(f"✓ {col} 列已保留")
            
            # 检查数据是否发生变化
            if col in df.columns:
                original_sample = df[col].head(3).tolist()
                filled_sample = filled_df[col].head(3).tolist()
                if original_sample == filled_sample:
                    print(f"  - {col} 列数据保持不变 ✓")
                else:
                    print(f"  - {col} 列数据发生变化 ⚠")
        else:
            print(f"✗ {col} 列丢失")
    
    # 验证数据完整性
    final_missing = filled_df.isnull().sum().sum()
    if final_missing == 0:
        print("✓ 所有缺失值已填充")
    else:
        print(f"⚠ 仍有 {final_missing} 个缺失值")
    
    # 6. 保存结果
    print("\n步骤6: 保存结果")
    print("-" * 30)
    
    output_file = "筛选后协变量特征值列表.csv"
    
    try:
        filled_df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"✓ 结果已保存到: {output_file}")
        
        # 显示最终统计
        print(f"\n处理结果统计:")
        print(f"  原始数据: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"  最终数据: {filled_df.shape[0]} 行 × {filled_df.shape[1]} 列")
        print(f"  变量保留率: {filled_df.shape[1] / df.shape[1] * 100:.1f}%")
        
        if existing_columns:
            covariate_cols = filled_df.shape[1] - len(existing_columns)
            print(f"  标识列数: {len(existing_columns)}")
            print(f"  协变量列数: {covariate_cols}")
        
        original_missing = df.isnull().sum().sum()
        final_missing = filled_df.isnull().sum().sum()
        print(f"  缺失值填充: {original_missing} → {final_missing}")
        
    except Exception as e:
        print(f"✗ 保存文件失败: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("✓ 所有处理步骤成功完成！")
    print("=" * 70)
    print(f"\n输出文件: {output_file}")
    print("现在可以运行主notebook进行后续分析")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n✗ 处理过程中出现错误，请检查并重试")
        exit(1)
