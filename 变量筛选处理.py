#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
变量筛选处理脚本
用于从原始的751个协变量中筛选出有效的特征变量

输入文件：194个国家1960-2023存在缺失值的751个协变量.csv
输出文件：筛选后协变量特征值列表.csv

筛选标准：
1. 缺失值比例 <= 50%
2. 数值变量方差 >= 0.01
3. KNN插值填充剩余缺失值
"""

import pandas as pd
import numpy as np
from utilts import filter_variables, min_max_knn_impute

def main():
    """
    主函数：执行完整的变量筛选流程
    """
    print("=" * 60)
    print("开始执行变量筛选处理")
    print("=" * 60)
    
    # 1. 读取原始数据
    print("\n步骤1: 读取原始数据文件...")
    input_file = "194个国家1960-2023存在缺失值的751个协变量.csv"
    
    try:
        # 尝试读取CSV文件
        original_df = pd.read_csv(input_file, encoding='utf-8')
        print(f"✓ 成功读取原始数据文件")
        print(f"  - 数据形状: {original_df.shape}")
        print(f"  - 原始变量数量: {original_df.shape[1]}")
        print(f"  - 数据行数: {original_df.shape[0]}")
        
    except UnicodeDecodeError:
        # 如果UTF-8编码失败，尝试其他编码
        print("UTF-8编码失败，尝试使用GBK编码...")
        try:
            original_df = pd.read_csv(input_file, encoding='gbk')
            print(f"✓ 使用GBK编码成功读取数据文件")
            print(f"  - 数据形状: {original_df.shape}")
        except Exception as e:
            print(f"✗ GBK编码也失败: {e}")
            print("尝试使用latin-1编码...")
            original_df = pd.read_csv(input_file, encoding='latin-1')
            print(f"✓ 使用latin-1编码成功读取数据文件")
            print(f"  - 数据形状: {original_df.shape}")
    
    except FileNotFoundError:
        print(f"✗ 错误：找不到文件 '{input_file}'")
        print("请确认文件名和路径是否正确")
        return False
    
    except Exception as e:
        print(f"✗ 读取文件时发生错误: {e}")
        return False
    
    # 2. 显示数据基本信息
    print(f"\n步骤2: 数据基本信息")
    print(f"  - 列名前5个: {list(original_df.columns[:5])}")
    print(f"  - 数据类型统计:")
    print(f"    * 数值型列: {len(original_df.select_dtypes(include=['number']).columns)}")
    print(f"    * 非数值型列: {len(original_df.select_dtypes(exclude=['number']).columns)}")
    
    # 检查缺失值情况
    missing_stats = original_df.isnull().sum()
    total_missing = missing_stats.sum()
    print(f"  - 总缺失值数量: {total_missing}")
    print(f"  - 缺失值比例: {total_missing / (original_df.shape[0] * original_df.shape[1]) * 100:.2f}%")
    
    # 3. 执行变量筛选
    print(f"\n步骤3: 执行变量筛选...")
    print("筛选参数:")
    print("  - 缺失值阈值: 50% (missing_threshold=0.5)")
    print("  - 方差阈值: 0.01 (variance_threshold=0.01)")
    
    try:
        # 调用筛选函数
        filtered_df = filter_variables(
            df=original_df, 
            missing_threshold=0.5,  # 50%缺失值阈值
            variance_threshold=0.01  # 0.01方差阈值
        )
        
        print(f"✓ 变量筛选完成")
        print(f"  - 筛选前变量数量: {original_df.shape[1]}")
        print(f"  - 筛选后变量数量: {filtered_df.shape[1]}")
        print(f"  - 剔除变量数量: {original_df.shape[1] - filtered_df.shape[1]}")
        print(f"  - 保留比例: {filtered_df.shape[1] / original_df.shape[1] * 100:.1f}%")
        
    except Exception as e:
        print(f"✗ 变量筛选过程中发生错误: {e}")
        return False
    
    # 4. KNN插值填充缺失值
    print(f"\n步骤4: KNN插值填充剩余缺失值...")
    
    # 检查筛选后的缺失值情况
    remaining_missing = filtered_df.isnull().sum().sum()
    print(f"  - 筛选后剩余缺失值: {remaining_missing}")
    
    if remaining_missing > 0:
        try:
            # 执行KNN插值
            filled_df = min_max_knn_impute(
                df=filtered_df,
                n_neighbors=5,  # 使用5个最近邻
                weights='uniform'  # 等权重
            )
            
            # 验证插值结果
            final_missing = filled_df.isnull().sum().sum()
            print(f"✓ KNN插值填充完成")
            print(f"  - 插值前缺失值: {remaining_missing}")
            print(f"  - 插值后缺失值: {final_missing}")
            
            if final_missing > 0:
                print(f"⚠ 警告：仍有 {final_missing} 个缺失值未被填充")
            
        except Exception as e:
            print(f"✗ KNN插值过程中发生错误: {e}")
            print("使用筛选后的数据（未插值）继续...")
            filled_df = filtered_df
    else:
        print("  - 无需插值，数据已完整")
        filled_df = filtered_df
    
    # 5. 保存结果
    print(f"\n步骤5: 保存处理结果...")
    output_file = "筛选后协变量特征值列表.csv"
    
    try:
        # 保存到CSV文件
        filled_df.to_csv(output_file, index=True, encoding='utf-8')
        print(f"✓ 成功保存结果文件: {output_file}")
        print(f"  - 最终数据形状: {filled_df.shape}")
        print(f"  - 最终变量数量: {filled_df.shape[1]}")
        
        # 显示最终统计信息
        print(f"\n最终处理结果统计:")
        print(f"  - 原始变量数量: {original_df.shape[1]}")
        print(f"  - 最终变量数量: {filled_df.shape[1]}")
        print(f"  - 变量保留率: {filled_df.shape[1] / original_df.shape[1] * 100:.1f}%")
        print(f"  - 数据完整性: {100 - (filled_df.isnull().sum().sum() / (filled_df.shape[0] * filled_df.shape[1]) * 100):.1f}%")
        
    except Exception as e:
        print(f"✗ 保存文件时发生错误: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("变量筛选处理完成！")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✓ 所有处理步骤成功完成")
        print(f"输出文件: 筛选后协变量特征值列表.csv")
        print("现在可以运行主notebook进行后续分析")
    else:
        print("\n✗ 处理过程中出现错误，请检查并重试")
