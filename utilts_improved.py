#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版的数据处理工具函数
专门针对包含location和year列的协变量数据集
"""

import pandas as pd
import numpy as np
from sklearn.impute import KNNImputer
from sklearn.preprocessing import MinMaxScaler

def filter_variables_improved(df, missing_threshold=0.5, variance_threshold=0.01, 
                            exclude_columns=['location', 'year']):
    """
    改进版变量筛选函数，排除指定的标识列
    
    :param df: 输入的 DataFrame
    :param missing_threshold: 缺失值比例阈值，默认 0.5（50%）
    :param variance_threshold: 方差阈值，默认 0.01
    :param exclude_columns: 需要排除筛选的列名列表
    :return: 处理后的 DataFrame
    """
    print(f"开始变量筛选，排除列: {exclude_columns}")
    
    # 获取需要筛选的列（排除标识列）
    columns_to_filter = [col for col in df.columns if col not in exclude_columns]
    df_to_filter = df[columns_to_filter]
    
    print(f"原始总列数: {len(df.columns)}")
    print(f"排除标识列后待筛选列数: {len(columns_to_filter)}")
    
    # 1. 剔除缺失值比例大于 missing_threshold 的变量
    missing_ratio = df_to_filter.isnull().mean()
    columns_to_drop_missing = missing_ratio[missing_ratio > missing_threshold].index
    
    print(f"剔除缺失值比例大于 {missing_threshold} 的变量数量: {len(columns_to_drop_missing)}")
    if len(columns_to_drop_missing) > 0:
        print(f"剔除的变量: {list(columns_to_drop_missing)[:10]}...")  # 只显示前10个
    
    # 2. 剔除方差小于 variance_threshold 的数值变量
    numeric_columns = df_to_filter.select_dtypes(include=['number']).columns
    variances = df_to_filter[numeric_columns].var()
    columns_to_drop_variance = variances[variances < variance_threshold].index
    
    print(f"剔除方差小于 {variance_threshold} 的变量数量: {len(columns_to_drop_variance)}")
    if len(columns_to_drop_variance) > 0:
        print(f"剔除的变量: {list(columns_to_drop_variance)[:10]}...")  # 只显示前10个
    
    # 3. 合并要删除的列
    all_columns_to_drop = set(columns_to_drop_missing) | set(columns_to_drop_variance)
    
    # 4. 保留的列 = 排除列 + 筛选后保留的列
    columns_to_keep = exclude_columns + [col for col in columns_to_filter 
                                       if col not in all_columns_to_drop]
    
    result_df = df[columns_to_keep]
    
    print(f"最终保留列数: {len(result_df.columns)}")
    print(f"其中标识列: {len(exclude_columns)}, 协变量列: {len(result_df.columns) - len(exclude_columns)}")
    
    return result_df


def min_max_knn_impute_improved(df, n_neighbors=5, weights='uniform', 
                              exclude_columns=['location', 'year'],
                              use_location_grouping=True):
    """
    改进版KNN插值函数，考虑location和year的特殊性
    
    :param df: 输入的 DataFrame
    :param n_neighbors: KNN 的邻居数量，默认 5
    :param weights: 权重计算方式，默认 'uniform'
    :param exclude_columns: 不参与标准化和插值的列
    :param use_location_grouping: 是否按location分组进行插值
    :return: 填充后的 DataFrame
    """
    print(f"开始KNN插值，排除列: {exclude_columns}")
    print(f"是否按location分组: {use_location_grouping}")
    
    # 1. 分离标识列和数值列
    identifier_columns = [col for col in exclude_columns if col in df.columns]
    numeric_columns = [col for col in df.columns if col not in exclude_columns]
    numeric_columns = df[numeric_columns].select_dtypes(include=['number']).columns.tolist()
    
    print(f"标识列: {identifier_columns}")
    print(f"待插值数值列数量: {len(numeric_columns)}")
    
    # 检查缺失值情况
    missing_before = df[numeric_columns].isnull().sum().sum()
    print(f"插值前缺失值总数: {missing_before}")
    
    if missing_before == 0:
        print("无缺失值，跳过插值")
        return df
    
    # 2. 复制原始数据
    result_df = df.copy()
    
    if use_location_grouping and 'location' in df.columns:
        # 按location分组进行插值
        print("按location分组进行插值...")
        
        for location in df['location'].unique():
            location_mask = df['location'] == location
            location_data = df.loc[location_mask, numeric_columns]
            
            # 检查该location是否有缺失值
            location_missing = location_data.isnull().sum().sum()
            if location_missing == 0:
                continue
                
            print(f"处理 {location}: {location_missing} 个缺失值")
            
            # 对该location的数据进行标准化和插值
            if len(location_data) >= n_neighbors:  # 确保有足够的样本
                # 标准化
                scaler = MinMaxScaler()
                location_scaled = pd.DataFrame(
                    scaler.fit_transform(location_data), 
                    columns=numeric_columns,
                    index=location_data.index
                )
                
                # KNN插值
                imputer = KNNImputer(n_neighbors=min(n_neighbors, len(location_data)-1), 
                                   weights=weights)
                location_imputed_scaled = pd.DataFrame(
                    imputer.fit_transform(location_scaled), 
                    columns=numeric_columns,
                    index=location_data.index
                )
                
                # 反标准化
                location_imputed = pd.DataFrame(
                    scaler.inverse_transform(location_imputed_scaled), 
                    columns=numeric_columns,
                    index=location_data.index
                )
                
                # 更新结果
                result_df.loc[location_mask, numeric_columns] = location_imputed
            else:
                print(f"  警告: {location} 样本数({len(location_data)})少于邻居数({n_neighbors})，跳过")
    
    else:
        # 全局插值（不分组）
        print("执行全局插值...")
        
        # 标准化
        scaler = MinMaxScaler()
        df_scaled = pd.DataFrame(
            scaler.fit_transform(df[numeric_columns]), 
            columns=numeric_columns,
            index=df.index
        )
        
        # KNN插值
        imputer = KNNImputer(n_neighbors=n_neighbors, weights=weights)
        df_imputed_scaled = pd.DataFrame(
            imputer.fit_transform(df_scaled), 
            columns=numeric_columns,
            index=df.index
        )
        
        # 反标准化
        df_imputed = pd.DataFrame(
            scaler.inverse_transform(df_imputed_scaled), 
            columns=numeric_columns,
            index=df.index
        )
        
        # 更新结果
        result_df[numeric_columns] = df_imputed
    
    # 验证插值结果
    missing_after = result_df[numeric_columns].isnull().sum().sum()
    print(f"插值后缺失值总数: {missing_after}")
    print(f"成功填充缺失值: {missing_before - missing_after}")
    
    return result_df


def check_data_structure(df):
    """
    检查数据结构，识别location和year列
    """
    print("=" * 50)
    print("数据结构检查")
    print("=" * 50)
    
    print(f"数据形状: {df.shape}")
    print(f"列名前10个: {list(df.columns[:10])}")
    
    # 检查是否有location列
    location_candidates = [col for col in df.columns if 'location' in col.lower()]
    print(f"可能的location列: {location_candidates}")
    
    # 检查是否有year列
    year_candidates = [col for col in df.columns if 'year' in col.lower()]
    print(f"可能的year列: {year_candidates}")
    
    # 检查数据类型
    print(f"\n数据类型统计:")
    print(f"数值型列数: {len(df.select_dtypes(include=['number']).columns)}")
    print(f"字符型列数: {len(df.select_dtypes(include=['object']).columns)}")
    
    # 检查缺失值
    missing_summary = df.isnull().sum()
    missing_cols = missing_summary[missing_summary > 0]
    print(f"\n有缺失值的列数: {len(missing_cols)}")
    print(f"总缺失值数: {missing_summary.sum()}")
    
    return {
        'location_candidates': location_candidates,
        'year_candidates': year_candidates,
        'missing_columns': len(missing_cols),
        'total_missing': missing_summary.sum()
    }
